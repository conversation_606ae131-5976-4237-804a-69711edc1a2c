<template>
  <div class="login-container">
    <div class="login-left-box">
      <img src="../assets/copywriter.png" alt="Spacetime&AIOS" class="main-logo" />
    </div>
    <div class="login-right-box">
      <img src="../assets/logo.svg" alt="logo" class="logo" />

      <div class="agreement-title">入驻协议</div>

      <div class="agreement-content">
        <h3>热爱元宇宙Spacetime&AlOS在线服务协议</h3>
        <div class="agreement-text">
          <p>商家使用“热爱元宇宙Spacetime&AlOS”、在热爱云居网站开展经营前请认真阅读并理解本协议内容，本协议内容中以加粗、下划线方式显著标识的文字，请商家着重阅读、慎重考虑。<br><br></p>
          <h3>前言</h3>
          <p>1、本协议的订立</p>
          <p>1.1本协议由热爱云居网站所有者“东莞热爱云居科技有限公司”（以下称“甲方” 简称热爱云居），与拟在热爱云居供应链平台以独立第三方经营者身份设立店铺进行经营的法律实体（以下称“乙方” 或“商家”、“卖家”）共同缔结，具有合同效力。</p>
          <p>1.2商家通过“热爱元宇宙Spacetime&AlOS”办理店铺入驻过程中，一经勾选“我已仔细阅读并同意协议”（具体内容以页面显示为准）， 并点击“提交入驻申请”按键，即意味着商家同意与热爱云居签订本协议并自愿受本协议约束，商家的入驻申请、资料提交等经过热爱云居审核通过时，立即生效，本协议对商家及热爱云居同时具有法律效力；若热爱云居平台审核后需要商家与热爱云居另行签署协议的，则商家还应遵守另行签署的协议。</p>
          <p>1.3本协议由协议正文、《反商业贿赂协议》等附件及公示于“热爱云居供应链平台” 的各项规则及其之后可能调整的规则共同组成。协议正文与规则冲突的，以发布在后的文件为准执行。</p>
          <p>1.4本协议仅在表明“热爱云居”与“商家”二者之间的权利义务关系，对于商家通过“热爱云居供应链平台”与 任何第三方建立的合作关系以及由此产生的权利义务关系等，由商家与第三方自行解决。</p>
          <h3>协议正文</h3>
          <h4>2、定义和解释</h4>
          <p>2.1“热爱云居”网站：指域名为 talmdcloud.com ，所有者为“东莞热爱云居科技有限公司” 的网站。用户可通过该网站发布、展示、查询、交流相关信息 ，达成交易意向，获得其他用户提供的服务等功能。如网站域名有调整，以热爱云居另行通知为准执行。</p>
          <p>2.2 热爱云居平台：指运行于“热爱云居 talmdcloud.com”网站、热爱云居相关的 APP、微信小程序以及热爱云居其他线上渠道，是热爱云居为用户提供信息发布、 交流，第三方经营者开设店铺经营，以及其他技术服务的电子商务服务平台。</p>
          <p>2.3 热爱云居平台用户：指所有在“热爱云居 talmdcloud.com”平台上注册的法人、自然人及其他组织，本协议中统称为“用户”、“客户”或“买家”，除非另有说明，否则“用户”、“客户”或“买家”，均指此含义。</p>
          <p>2.4 商家注册及入驻：商家注册指欲成为“热爱云居平台”第三方经营者的商家，依据“热爱云居平台 ”入驻 流程和要求完成在线信息提交，经“热爱云居”审核同意后，商家可以使用设定的热爱云居平台用户名和密码登陆 “热爱云居平台”，以开立店铺开展经营；商家入驻，亦称“店铺入驻”指第三方经营者完成注册，通过资质审核且满足本协议第四条店铺服务开通条件后成为“热爱云居平台”第三方经营者的过程；本协议中的商家指本协议缔约方中的“乙方”。</p>
          <p>2.5店铺：商家在完成商家入驻流程后，为进行合法经营，依据协议约定和热爱云居平台规则由商家申请， 热爱云居审核通过的具有独立且唯一性 ID（英文 Identity document 的缩写，意为“身份标识号码”,此处指“商家 ID”）、 特定名称（可依据相关平台规则进行调整） 的网络化虚拟商铺。商家通过热爱云居平台用户名可以实现对该店铺的管理，包括但不仅限于商品信息上传、修改、删除处理，交易跟踪、售后及管理等。“店铺用户名”：是指商家完成商家注册后获取的与自设密码共同使用以登陆使用“热爱云居平台”店铺服务的登陆账户，每一个热爱云居平台用户名对应一个店铺，一个店铺下可自行设置多个子账号用于店铺内部管理使用。商家应妥善保管店铺用户名及密码信息，不得擅自转让或授权他人使用，并对其店铺用户名下的一切行为独立承担责任。</p>
          <p>2.6 店铺服务期：指热爱云居按本协议第五条为商家开通店铺权限后商家可持续经营的期限，除非双方一致同意终止服务期或者满足本协议约定的终止情形，商家可在热爱云居持续经营。首个服务期自缴费权限开通之日起至2024年 12 月 31 日止，若商家在2024年12月 31 日前未完成缴费操作，则视为商家自动放弃经营，平台将终止与其合作。</p>
          <p>2.7 缴费权限：商家完成协议签署、资料提交并经热爱云居审核通过，有能力开展店铺经营时，热爱云居为商家开通的缴费环节对应的操作权限。此权限一经开通，店铺服务期即开始计算，(热爱云居指定的免费期以店铺入驻审核通过开始计算服务期)。</p>
          <p>2.8 热爱云居平台规则 ：指标示在“热爱云居平台”之上的，需商家实时关注的，与商家经营有关的任何规范性文件，包括但不限于入驻协议、商家手册、商家后台公告、商家帮助中心等。商家知晓并同意，热爱云居有权单方更新、调整热爱云居平台规则，商家进一步知晓并同意，如不同意接受调整后的热爱云居平台规则时，应终止本协议，停止在热爱云居平台的店铺经营。</p>
          <p>2.9商品：亦称 “产品”：指乙方生产、经销或提供的产品或服务，必须符合国家、行业及双方约定标准，并不侵犯任何第三方合法权益，包含产品附件、配件、随机文件资料、赠品、礼品、奖品、试用品或样品及相关服务等。</p>
          <p>2.10保证金：指商家根据本服务条款、服务协议及“热爱云居平台”相关规则，向“热爱云居”缴纳的， 在商家未履行消费者权益保障义务、违反服务协议或者“热爱云居平台”相关规则或流程时，用于对买家(指通过“热爱云居平台”购买商家商品的用户，以下均称“买家”)进行赔付或对“热爱云居”或买家支付违约金的资金。</p>
          <p>2.11平台使用费：商家使用“热爱云居平台”各项服务时按照本协议约定缴纳的固定技术服务费用。</p>
          <p>2.12优惠权益工具：热爱云居向商家提供的，可用于在商家店铺中享受优惠和折扣的系统工具，包括但不限于热爱云居积分兑换、优惠券及热爱云居后续提供的其他类型的优惠权益。</p>
          <p>2.13商家关联店铺：是指与商家存在关联关系的店铺，包括但不限于：以关联人士名义创建的店铺；同一经营主体的店铺；不同经营主体但存在同一法人代表、投资关系等关系的店铺；或通过技术手段识别出由同一实际控制人经营的店铺。<br><br></p>
          <h4>3、服务内容</h4>
          <p>3.1 热爱云居向商家提供“热爱云居平台”上相应的网络空间、技术支持和系统维护，以及同意向商家提供的各项附属功能、增值服务等，以使商家作为销售者有能力独立开设店铺、上传并维护商品或服务信息、与意向客户洽商交易以及开展与交易有关的各项业务。</p>
          <p>3.2商家获得“热爱云居 talmdcloud.com”使用权限后，可独立开展店铺经营 ，同时可参与平台相关活动及接受其他有偿服务。其他有偿服务包括但不限于广告推广服务、配送仓储服务、支付结算服务等。商家如需使用有偿服务的，应以商家自身名义单独与热爱云居洽谈并签署协议。</p>
          <p>3.3商家的经营行为受热爱云居的监督和管理。热爱云居依据法律法规、本协议约定以及“热爱云居平台规则”，有权单方决定限制商家的经营行为，比如限制“热爱云居 talmdcloud.com”权限，要求商家删除或调整发布的信息等，商家拒绝履行或配合时，热爱云居有权根据本协议及“热爱云居平台规则”采取相应措施；热爱云居同时有权将商家违法、违规行为举报或上报到市场监督管理局等国家监管部门。</p>
          <p>3.4 商家的经营行为受消费者的监督，消费者可以通过“热爱云居 talmdcloud.com”评价功能对商家的商品和服务进  行评价，该评价将以分值的形式体现商家商品及其服务的优劣；消费者也可以通过向热爱云居、监管机关、媒体等进行投诉进行监督，热爱云居知悉该投诉后，将介入调查并向商家出具意见，商家应积极配合并妥善解决。</p>
          <p>3.5 商家的经营行为受国家监管机构的监督和管理。热爱云居收到市场监督管理局等监管部门监管意见时， 有权向监管机关提供相关资料，要求商家予以整改、执行监管意见等；商家怠于执行或迟延执行时，热爱云居有权限制“热爱云居 talmdcloud.com”权限，删除或调整商家发布的信息等，由此产生的不利后果由商家自行承担。<br><br></p>
          <h4>4、资格要求及证明文件</h4>
          <p>4.1 资格要求</p>
          <p>商家申请及开展店铺经营活动，须持续的同时满足以下基本条件：</p>
          <p>4.1.1 商家已依照中华人民共和国法律注册并领取合法有效的营业执照及取得其他经营许可；如商家为自然人，应为年满 18 周岁，具有完全民事行为能力的中国公民；身份信息应为商家自身情况的客观表现，商家应具备签署、履行本协议的合法资格；</p>
          <p>4.1.2 商家经营的商品来源合法，资质齐全、不侵犯任何第三方著作权、商标权、专利权等知识产权权利权益、及其他合法权益；</p>
          <p>4.1.3 商家提交的任何信息均真实、合法、有效，所使用的视频、图片、文字等不侵犯任何第三方著作权、商标权、专利权等知识产权权利权益、及其他合法权益；</p>
          <p>4.1.4 商家自愿签署并严格履行本协议；</p>
          <p>4.1.5 热爱云居依据法律法规等要求及经营需要可能设定的其他条件。</p>
          <p>4.2 证明文件</p>
          <p>4.2.1 商家须依据“ 热爱云居 talmdcloud.com”所示在线提交各项必须文件或证明，包括但不限于营业执照复印件、开户行证明、授权委托书、商标注册证、身份证正反面复印件、银行经营流水、安全许可证、环保认证、信用等级证等。</p>
          <p>在发生客户投诉、行政机关机构调查、诉讼解决等事项时，商家还应向热爱云居提交加盖商家公章的复印件或电子扫描件。商家为自然人的，应向热爱云居提交商家亲笔签字的复印件或电子扫描件。</p>
          <p>4.2.2 商家保证向热爱云居提供的上述证明文件或其他相关证明真实、合法、准确、有效并单独承担全部法律责任 ，热爱云居有权自行或单方聘请第三方公司予以核实。</p>
          <p>4.2.3 商家保证上述证明文件发生任何变更或更新时立即通知热爱云居，并在系统中提交或联系平台人员提交更新后的文件。</p>
          <p>4.2.4 商家提交虚假、过期文件、或未如期通知并提交更新文件或未及时更新或通知证明文件导致纠纷或被相关国家机关处罚等情形的，由商家独立承担全部法律责任。若由此导致商家不符合店铺入驻条件的， 热爱云居有权要求商家补充提供相关资料，或者拒绝商家申请、调整“热爱云居 talmdcloud.com”权限、直至终止本协议。如商家造成热爱云居及其他任何第三方损失的，商家还应足额赔偿。</p>
          <p>4.3 商家自荐文件</p>
          <p>如按照热爱云居要求商家入驻前需提供自荐材料的，商家应保证提交的自荐材料真实、准确、有效。若发现自荐材料内容与实际不符的，热爱云居有权单方终止合作。<br><br></p>
          <h4>5、协议方权利及义务</h4>
          <p>5.1 “热爱云居”根据本协议向乙方提供电子商务交易平台及相应技术支持服务，在现有技术可实现的基 础上维护“热爱云居平台”的正常稳定运行，并努力提升和改进技术，对平台功能及服务进行更新、升级，不断提升平台性能和交易效率。该更新升级可能影响乙方店铺的全部或部分店铺功能，给乙方导致不便，乙方对此予以完全理解和接受，热爱云居对该更新升级不承担任何责任。</p>
          <p>5.2 热爱云居有权根据热爱云居平台的发展规划自主选择为申请入驻的商家开通权限，而未承诺必然对所有申请入驻的商家承诺开通权限；同时有权依据独立判断，不受时间限制的审批商家的入驻申请。</p>
          <p>5.3 热爱云居对商家在使用热爱云居平台过程中遇到的问题及提出的建议和意见予以积极回复 ，可依商家需求对其使用热爱云居平台提供合理的指导和培训。</p>
          <p>5.4 热爱云居有权单方根据国家相关法律法规、政策及热爱云居平台运营情况，对公示于热爱云居平台规则进行变更，变更后的规则将以公告形式告知商家，任何变更一经公告即构成本协议的组成部分。商家应实时关注公告内容，如不同意该变更，应书面通知热爱云居并立即停止使用“热爱云居 talmdcloud.com”，一旦商家继续使用“热爱云居 talmdcloud.com”，则视为商家自愿接受变更后的规则。</p>
          <p>5.5 商家应对店铺负有管理义务，对其店铺中出现的违反国家有关法律、法规规定及热爱云居规则的信息予以立即删除。否则对商家前述不当行为，“热爱云居”有权追究其违约、侵权责任并/或解除本协议。</p>
          <p>5.6 热爱云居有权将国家生效法律文书或行政文书确定的商家违法违规事件，或商家已确认的商家违反本协议相关约定的事项，或热爱云居依据本协议及平台规则对商家违反法律、法规的行为实施的警示、暂停或者终止服务等措施，在热爱云居平台上予以公示；商家违规或者有严重违约、违规情形的，“热爱云居”有权对其采取暂停店铺运营、扣除保证金直至终止本协议等措施，上述措施不足以补偿热爱云居损失的，“热爱云居”有权继续向商家追偿。如商家的运营情况不能满足热爱云居平台公布的要求（包括但不限于热爱云居平台规则等），经限期整改调整后，仍无法满足的，热爱云居有权解除本协议，停止向商家提供服务。</p>
          <p>5.7 商家知悉并同意：商家因开设店铺及其经营需要、履行本协议义务而向热爱云居提交的任何信息和数据，以及商家在经营过程中产生的交易数据、店铺及商品评价数据，热爱云居有权在本协议终止后继续保留，同时，无论本协议终止前还是终止之后，热爱云居均有权合理使用，及无义务返还商家、也无义务删除原始数据及其备份、同时无义务就使用该数据的行为向商家支付任何费用。热爱云居收集、记录的商家在使用本服务过程中及其他运营过程中所产生的汇总数据的相关权利及权益归属于热爱云居，未经热爱云居事先书面同意，商家不得为本协议约定之外的目的使用前述数据，亦不得以任何形式将前述数据提供给他人。</p>
          <p>5.8 商家知悉并同意:商家在平台运营的相关系统需与与入驻热爱云居开放物流平台的快递公司系统进行对接，用于处理订单配送等事宜，商家须有效保护用户个人信息，否则产生的不利后果由商家承担。</p>
          <p>5.9 商家知悉并同意，为更好地提升商家整体服务品质和经营能力，不断提高用户体验，热爱云居有权制定周期性不等考核标准，对商家的经营情况进行不同维度考核。热爱云居将以具体规则形式在热爱云居平台公示或以邮件方式将考核处理结果通知商家。商家认可并同意接受热爱云居的上述考核，并愿意按热爱云居公布的考核标准执行，接受考核后处理结果。<br><br></p>
          <h4>6、商家声明及保证</h4>
          <p>6.1 保证遵守本协议，不从事任何有损热爱云居利益的行为。</p>
          <p>6.2 保证符合本协议约定的资格要求，保证在“热爱云居 talmdcloud.com”中在线提交的信息和证明文件真 实、准确、合法、有效，并保证在上述资料发生变更时及时通知热爱云居，并予以更新。商家应保证入驻时提供的联系人、联系地址、联系电话等信息真实、有效、完整，并对此承担法律责任。</p>
          <p>6.3 保证订立本协议和在“热爱云居 talmdcloud.com”提出申请是商家真实意思表示，保证其具有足够资格订立本协议，其代理人（包括所有操作“热爱云居 talmdcloud.com”的人员和店铺运营所需的全部雇员、职员、 管理者） 已获得充分授权，并对代理人的行为承担法律责任，同时商家保证对其雇员、联系人及其他商家委派的履行本协议的人员的行为承担法律责任。</p>
          <p>6.4 商家应当为每一个“店铺用户名”设定独立的、高安全等级的密码，保证妥善保管“店铺用户名” 及密码，不得擅自转让或授权他人使用，并对利用该用户名所进行的一切活动负全部责任。</p>
          <p>6.5 保证严格按照本协议约定、技术要求使用“热爱云居 talmdcloud.com”，不从事攻击、破译、反向工程， 上传木马、病毒等有损该系统安全和稳定的操作。</p>
          <p>6.6 保证拥有在热爱云居平台经营商品的合法销售权，商品来源可溯、合法、质量合格，符合国家相关质量标准，不侵犯任何第三方的合法权利，并对其商品质量及商品合法性独立承担全部法律责任。</p>
          <p>6.7 商家同意，有关商品/服务相关信息由商家自行上传、发布至相关甲方展示渠道并由商家进行维护。 商家保证履行“如实描述”义务，保证在热爱云居平台发布的商品或服务信息真实、准确，符合热爱云居平台规则及国家相关法律法规的规定，与实际出售的商品一致，不含任何夸大或虚假内容 ，不包含任何未经品牌授权的文字和图片。保证其提供给热爱云居或上传至热爱云居平台的图片、音频、视频等文件资料中涉及的相关知识产权及人物人身权享有合法权利或已获得合法授权，不侵犯任何第三方的合法权益。商家对商品信息描述  的准确、相符及上述文件资料的合法有效负有举证责任并承担独立的完全的法律责任。</p>
          <p>6.8 保证向购买其商品的热爱云居用户开具合法合规发票，并保证按照国家相关规定自行承担相应税费。 如因商家发票开具错误、未开具发票或者其他发票问题引起的纠纷，均由商家独立承担相应的法律责任， 如造成热爱云居损失的，商家应予以赔偿。</p>
          <p>6.9 商家同意遵守个人信息安全保护要求：</p>
          <p>6.9.1商家同意并保证，商家在使用热爱云居服务过程中须遵循合法、正当、必要原则收集或使用个人信息，且仅能为满足为用户/消费者提供商品/服务所必须，并均已经过该用户/消费者明确授权（涉及用户敏感信息 的，还需要用户单独同意）， 严格依据向用户/消费者所公示的隐私政策或个人信息处理规则予以处理，不存在过度收集、使用或未经授权的转移个人信息等情况。</p>
          <p>6.9.2商家不得泄露、出售或者非法向他人提供个人信息。在发生或者可能发生信息泄露、丢失的情况时， 商家应当立即采取补救措施，并承担由此产生的责任，热爱云居因此遭受损失的，商家应进行全额赔偿。</p>
          <p>6.9.3用户主动提出删除其个人信息或者撤回个人信息使用授权时，商家应及时按照用户要求进行数据处理或者配合热爱云居进行相应的数据处理。商家理解并同意，在能够正常实现服务的前提下，热爱云居可能基于法律 法规、监管要求，为保护个人信息安全而对用户/消费者信息采取脱敏等安全措施，商家能够积极配合。如 商家未严格履行上述数据保护义务或者存在其他违反相关法律法规和监管要求明确的数据保护义务，给用户或热爱云居造成损失的，商家应全额赔偿用户或热爱云居因此遭受的损失。</p>
          <p>6.10 保证不将从热爱云居平台获取的任何数据用于本协议约定以外的用途 ，同时保证未经热爱云居许可不得擅自获取、使用、传播热爱云居平台的任何资料，包括但不限于交易数据、用户信息、支付信息、热爱云居其他用户展示于热爱云居平台的信息等。</p>
          <p>6.11 商家不得隐瞒任何可能对热爱云居平台产生风险的事项，包括但不限于产品出现设计缺陷、质量瑕疵、权利纠纷、重大违约、上传资料包含病毒木马等，若商家发生此类影响热爱云居平台商誉、正常经营、 安全的事项而商家未及时通知热爱云居的，热爱云居有权解除本协议并追究商家违约、侵权责任。</p>
          <p>6.12 未经热爱云居另行书面许可，商家不得将本协议项下权利义务全部或部分转移给第三方；商家聘请或委托第三方代表商家运营店铺的，第三方的一切行为均视同为商家亲自实施，</p>
          <p>由商家承担全部法律后果；商家与第三方之间的权利义务关系由商家与第三方单独解决，不得影响到热爱云居及热爱云居其他用户的权利。</p>
          <p>6.13 商家不得在任何国家或地区抢先申请或注册热爱云居及其关联公司的任何知识产权（包括商标、 专利、著作权、域名、软件开发脚本及平台软件等）信息，如果商家违反此条款，商家承诺该违约注册的知识产权归热爱云居所有，承诺无偿将该知识产权转让给热爱云居，承诺配合签署转让文件并承担转让费用、办理管理权转移。商家此等行为给热爱云居造成损失的（包括但不限于诉讼赔偿、诉讼费用、律师费用等），商家应予以全部赔偿。</p>
          <p>为明确含义，抢注的情形包括但不限于： 1）商家知悉的热爱云居尚未申请注册的信息；2）热爱云居已申请区分表某类商品服务上的商标注册，但未在区分表其他商品或服务类别上申请商标注册的信息；3）热爱云居已申请一种知识产权，但尚未在其他种类知识产权权利上申请的信息；4）热爱云居已在一国申请，尚未在其他国家或地区申请注册的信息；5）以上信息包括抢注相同的信息，也包括抢注近似的信息。</p>
          <p>商家保证不得对任何热爱云居系统或平台进行反向工程、反编译或反汇编，不得开发与热爱云居系统或平台有关的衍生品、服务、插件、外挂、兼容、互联等。非经热爱云居或热爱云居授权开发并正式发布的任何由热爱云居系统衍生的软件均属非法软件，由此产生的法律责任与纠纷与热爱云居无关，违反本条款的，热爱云居有权中止或终止本协议全部或部分内容。<br><br></p>
          <h4>7、费用和结算</h4>
          <p>7.1 保证金： 商家知悉并同意向热爱云居缴纳相应金额的保证金，作为履行本协议尤其是消费者权益保障义务的 保证，并进一步知悉且同意在商家违反本协议相关约定时，热爱云居有权根据相关约定扣除相应金额的保证金 作为违约金或给予买家等第三方的赔偿。</p>
          <p>7.1.1 保证金的管理和使用</p>
          <p>7.1.1.1 在下述情形下，热爱云居有权根据具体情况直接扣除部分或全部保证金：</p>
          <p>1)&nbsp;&nbsp;&nbsp;商家违反本协议项下的任何保证、承诺或义务的；</p>
          <p>2)&nbsp;&nbsp;&nbsp;商家的行为违反国家法律法规及其它规范性文件等规定的；</p>
          <p>3)&nbsp;&nbsp;&nbsp;商家在热爱云居平台发布商品、达成交易、履行交易相关活动中，违反法律法规、政策、热爱云居平台任何规则或违反其对客户的承诺，或被客户投诉、索赔时，热爱云居判断应对客户进行赔付的；</p>
          <p>4)&nbsp;&nbsp;&nbsp;商家违反其与热爱云居或热爱云居关联公司/机构的其他协议或热爱云居平台任何规则，给热爱云居或其关联公司/机构造成任何损失（包括但不限于诉讼赔偿、诉讼费用、律师费用等）的；</p>
          <p>5)&nbsp;&nbsp;&nbsp;商家的行为被司法机关认定为刑事犯罪，热爱云居有权扣除其全部保证金；</p>
          <p>6)&nbsp;&nbsp;&nbsp;本协议中约定的其它可扣除保证金的情形出现的。</p>
          <p>7.1.1.2 热爱云居如使用保证金进行任何抵扣或赔付，将以书面方式（包括但不限于电子邮件、传真等） 通知商家，并在书面通知中，说明抵扣和/或赔付原因及抵扣和/或赔付的金额。</p>
          <p>7.1.1.3 若商家保证金不足时，热爱云居没有使用自有资金替商家支付赔偿金、补偿金、抚恤金或其他任 何款项的义务，但若热爱云居进行了该等支付，则热爱云居有权要求商家于五日内足额赔偿。</p>
          <p>7.1.1.4 保证金的退还</p>
          <p>服务协议终止且服务协议所约定的特定店铺所有订单终止、所有争议及索赔已处理完毕、已完成交易的商品质保期届满且商家按本协议约定完成终止店铺运营的公示后，商家可向热爱云居提出退还保证金的书面申请，热爱云居审核通过后一个月内（热爱云居平台另有约定、公示或通知的除外） ，扣除依据协议应扣除的各项费用后，将保证金余额退还商家，如商家支付的保证金，不足以抵扣应由商家支付违约金、赔偿金等款项的，热爱云居将不予退还商家保证金，并保留向商家追偿的权利。</p>
          <p>7.2 技术服务费</p>
          <p>商家在申请入驻及经营过程中，热爱云居提供相关的系统维护和支持服务，而向商家收取的技术服务费：</p>
          <p>7.2.1 固定技术服务费（亦称“平台使用费”）：商家在获得缴费权限后按照缴费通知所载明内容向 热爱云居支付的技术服务费。</p>
          <p>7.2.2 增值服务费</p>
          <p>商家使用热爱云居或热爱云居指定第三方提供的增值服务时，应按照另行签订的协议履行其义务并支付相关费用。</p>
          <p>7.2.3 税费</p>
          <p>商家在经营过程中，应自行承担相关税费，热爱云居无义务为商家代扣代缴。<br><br></p>
          <h4>8、保密义务</h4>
          <p>8.1 一方对于本协议的签订、内容及在履行本协议期间所获知的其他方的商业秘密负有保密义务。非经相对方书面同意，不得向相对方以外的任何第三方泄露、给予或转让该等保密信息。（根据法律法规、证券交易所规则向政府、证券交易所和/或其他监管机构供、协议方的法律除外）。</p>
          <p>8.2 如相对方提出要求，任何一方均应将载有相对方单独所有的保密信息的任何文件、资料或软件等， 在本协议终止后按对方要求归还对方，或予以销毁，或进行其他处置，并且不得继续使用这些保密信息。</p>
          <p>8.3 在本协议终止之后 4 年内，各方仍需遵守本协议之保密条款，履行其所承诺的保密义务，直到其他方同意其解除此项义务，或事实上不会因违反本协议的保密条款而给其他方造成任何形式的损害时为止。</p>
          <p>8.4 任何一方均应告知并督促其因履行本协议之目的而必须获知本协议内容及因合作而获知对方商业秘密的雇员、代理人、顾问等遵守保密条款，并对其雇员、代理人、顾问等的行为承担责任。</p>
          <p>8.5 热爱云居依据本协议正文第 5.7 条约定使用商家相关数据时，除不得直接体现商家身份信息外，不受本第8条的保密限制。<br><br></p>
          <h4>9、违约责任</h4>
          <p>9.1 商家的产品及经营等一切行为导致纠纷或政府部门查处的，在接到热爱云居通知后三个自然日内， 商家应按热爱云居的要求提供相应的证明材料，必要时应自行或由热爱云居协助或授权热爱云居及/或其关联公司/机构处理解决。由此导致的热爱云居及/或其关联公司/机构损失（损失包括但不限于诉讼费、律师费、检测费、鉴定费、赔偿款、补偿款、行政机关处罚、差旅费、商誉等），商家应足额赔偿。</p>
          <p>9.2 商家承诺不在热爱云居平台销售假冒商品、水货、旧货、不合格产品、侵犯第三方知识产权权利权益的商品或服务，以及违反法律禁止性规定或违反热爱云居平台规则的商品、服务等，否则如销售假冒商品， 则热爱云居有权要求商家就该店铺支付人民币 50 万元或该店铺全部累计销售额20倍的金额（二者以高者为准）作为惩罚性违约金；如违反上述其他约定的，则甲方有权要求乙方支付人民币10万元或全部保证金金额（二者以高者为准）作为惩罚性违约金。</p>
          <p>上述违约金不足以弥补热爱云居损失的，商家还应全部赔偿（包括但不限于热爱云居对客户的赔偿、 补偿、行政部门的处罚、律师费、诉讼费、鉴定费、差旅费等）。上述违约金的支付并不影响热爱云居依照本协议或平台规则的约定扣除相应的保证金。</p>
          <p>9.3商家不论采取何种方式将热爱云居用户吸引到“热爱云居平台”以外的平台或场所进行交易或绕开热爱云居指定付款方式进行交易的，以及非法获取“热爱云居talmdcloud.com”数据、利用“热爱云居平台”谋取不正当利益或从事非法活动的，热爱云居有权扣除商家全部保证金作为违约金，并保留向商家继续追究违约责任的权利。</p>
          <p>9.4商家发生违反本协议及热爱云居平台规则的情形时，热爱云居除有权按照本条约定要求商家承担违约责任外，还有权按照“热爱云居平台”相关管理规则采取商品立即下架、暂停向商家提供服务、暂时关闭商家管理账户、终止合作等措施。如商家销售假冒商品，热爱云居有权将商家及其关联方加入合作黑名单一并清退。</p>
          <p>9.5本条中涉及的违约金及赔偿金，商家应在 5日内热爱云居支付，若商家逾期不予缴纳，热爱云居有权从待向商家及其关联公司结算的任意一笔款项或商家及其关联公司交纳的任意一笔保证金中直接扣除。<br><br></p>
          <h4>10、有限责任及免责条款</h4>
          <p>10.1不论在何种情况下，热爱云居均不对由于电力、网络、电脑、通讯或其他系统的故障、罢工（含内部罢工或劳工骚乱）、劳动争议、暴乱、起义、骚乱、生产力或生产资料不足、火灾、洪水、风暴、爆炸、 战争、政府行为等不可抗力，国际、国内法院的命令或第三方的不作为而造成的不能服务或延迟服务承担责任。</p>
          <p>10.2 鉴于互联网及电子商务所具有之特殊性质，热爱云居的免责事由亦包括但不限于下列任何影响网络正常经营之情形：（1）大规模黑客攻击、计算机病毒侵入或发作；（2）计算机系统遭到破坏、瘫痪或无法正常使用而导致热爱云居不能履行本服务协议下义务；（3）电信部门技术调整导致之重大影响；（4）因政府管制而造成的暂时性关闭等；（5）其他非热爱云居过错造成的情形。</p>
          <p>10.3 使用“热爱云居平台”下载数据或者获取任何资料的行为均出于商家的独立判断，并由其自行承担因此而可能产生的风险和责任。<br><br></p>
          <h4>11、协议有效期</h4>
          <p>11.1 本协议自生效时起持续有效。</p>
          <p>11.2 本协议仅对商家基于本协议而设立的特定店铺开展的相关业务具有法律约束力，不对“热爱云居” 与商家开展的同等或类似业务具有法律约束力，如“热爱云居”与商家同时开展同等或类似业务，应依据另行签订的协议执行，本协议另有约定的除外。<br><br></p>
          <h4>12、协议的终止</h4>
          <p>12.1 本协议在下述情形下自然终止：</p>
          <p>12.1.1 店铺服务期届满，而商家未按照本协议约定完成缴费或者未按照热爱云居通知补签相关协议的；</p>
          <p>12.1.2 协议方签署新协议替代本协议的。</p>
          <p>12.1.3 商家店铺服务停止或被停止持续时间达到一个月的。</p>
          <p>12.2 通知终止：除本协议另有约定外，热爱云居可提前 15（十五）日以邮寄、电子邮件、短信、即时通讯工具（如微信、QQ）等书面形式通知商家终止本协议。如商家依据本协议提前终止合作的，须向热爱云居提出停止店铺经营的申请，经审核同意，商家店铺将立即进入“终止经营提前公示”状态（终止经营提前公示，是指在一定时间内商户店铺 首页显示该店铺即将终止经营的公告信息），公示结束时本协议终止。</p>
          <p>本协议签署后，为使商家顺利开展经营，热爱云居已提供人力、物力、技术支持和服务，如因前述原因导致本协议提前终止的，商家同意热爱云居不退还已缴纳的平台使用费，未缴纳的，商家同意足额缴纳。</p>
          <p>12.3 商家有下述情形时，热爱云居可单方终止本协议：</p>
          <p>12.3.1“热爱云居 talmdcloud.com”权限停止的；</p>
          <p>12.3.2 商家出现负面消息等可能影响“热爱云居平台”商誉的（包括但不限于商家因其商品及经营等行为引起客户向热爱云居大量投诉（累计因同一商品或经营行为投诉 5 件以上的）、被行政机关确认违法、被法院认定违法等。）；</p>
          <p>12.3.3 经热爱云居自查发现商家有销售假货、水货、旧货、不合格产品、侵犯第三方知识产权权利权益的商品或服务，以及违反法律禁止性规定或违反热爱云居平台规则的商品、服务等的；</p>
          <p>12.3.4 商家连续 60（六十）日，未登陆“热爱云居 talmdcloud.com” 的；</p>
          <p>12.3.5 本协议其他相应条款中，约定热爱云居有权终止本协议的情形发生的。</p>
          <p>12.4 协议终止后有关事项的处理</p>
          <p>12.4.1协议终止日起 ，热爱云居将停止“热爱云居 talmdcloud.com”全部权限， 同时，热爱云居无义务在“热爱云居 talmdcloud.com”上继续展示任何商家及其商品或服务信息。</p>
          <p>12.4.2本协议终止后，热爱云居有权保留商家的注册信息及交易行为记录等数据，但热爱云居没有为商家保留这些数据的义务，亦不承担在协议终止后向商家或第三方转发任何未阅读或未发送的信息的义务，也不就协议终止向商家或任何第三方承担责任 ，国家法律法规另有规定的除外。</p>
          <p>12.4.3本协议终止之日起三个月内，“热爱云居”与商家进行清算，包括但不限于财务对账及在途货物的处理。在途货物指本协议终止前 ，客户已购买但尚未收到的商品，对于此类商品，商家仍应按照合作终止前的流程交付并结算。 同时，热爱云居有权将协议终止事项告知该等客户，客户有权选择是否继续履行交易， 商家同意无条件接受客户的选择。</p>
          <p>12.4.4 本协议的终止，并不免除商家依据本协议应向客户承担的售后服务及产品保证责任，商家仍应按照履行售后服务义务及产品质量保证责任；如因商家商品质量问题或售后服务问题而导致热爱云居或其他第三方人身或财产损失的，商家仍应独立承担全部责任。</p>
          <p>12.4.5 本协议终止后，如发生“热爱云居”收到有关商家的投诉、举报、起诉等情形的，商家需积极配合“热爱云居”处理，如因此导致“热爱云居”损失的，商家应独立承担全部责任。<br><br></p>
          <h4>13、争议解决</h4>
          <p>13.1 履行本协议过程中产生的任何争议，协议方应协商解决，协商不成的，任一方有权将争议提交甲方所在地有管辖权的人民法院诉讼解决。</p>
          <p>13.2 本协议的签订、解释、变更、履行及争议的解决等均适用中华人民共和国大陆地区现行有效的法律。<br><br></p>
          <h4>14、其他约定</h4>
          <p>14.1 本协议的任何一方未能及时行使本协议项下的权利不应被视为放弃该权利，也不影响该方在将来行使该权利。</p>
          <p>14.2 如果本协议中的任何条款无论因何种原因完全或部分无效或不具有执行力，或违反任何适用的法律，则该条款被视为删除 ，但本协议的其余条款仍应具有法律约束力。<br><br></p>
          <h4>15、协议附件</h4>
          <p>15.1 附件一：《反商业贿赂协议》<br><br></p>
          <p>附件一</p>
          <h4>反商业贿赂协议</h4>
          <p>各方合作期间，为了更严格遵守《反不正当竞争法》及其他相关法律法规有关禁止商业贿赂行为的规定，维护各方共同利益，促进关系良好发展，经各方友好协商，达成如下协议：</p>
          <p>1、本协议中的商业贿赂是指商家（含商家关联方或合作方）为谋求交易机会或竞争优势及其他合作的利益，商家或商家单位工作人员或商家通过第三方给予热爱云居客户、热爱云居合作方、热爱云居员工及热爱云居员工利害相关人的一切物质及精神上的直接或间接的不正当利益，或从热爱云居合作方及其员工处收受一切物质及精神上的直接或间接的不正当利益。</p>
          <p>2、不正当利益：包括物质性利益和非物质性利益。物质性利益是指能够直接用金钱价值加以衡量的利益。包括但不限于回扣、贿赂、私下佣金、借款、实物、现金或现金等价物（如：消费卡/券、提货券、 购物卡、换购券、充值卡、交通卡、电话卡、各种话费的充值或其它可供使用或消费的充值、储值卡及其   它形态的有价礼券或证券等）、支票及财产性权益、旅游、宴请、免费消费。非物质性利益是指难以直接用经济或金钱价值加以衡量的能满足人们需求和欲望的精神利益和其他不正当利益，是物质性利益以外的权益、优惠、便利以及其它好处。包括但不限于给予解决住房机会、迁移户口、调动工作、提拔职务、安排出国留学、享受免费的服务等方面的利益，以及给予荣誉、名誉、称号、资格、地位、特权。</p>
          <p>3、利益冲突：包括但不限于（1）商家不得向热爱云居客户、热爱云居员工及热爱云居利害相关人提供任何形式的借款；（2）商家的股东、监事、经理、高级管理人员、合作项目负责人及项目成员系热爱云居客户、热爱云居员工或热爱云居利害相关人员的，应在合作前以书面方式如实、全面告知热爱云居。（3）各方合作过程中，商家不得允许热爱云居员工及其配偶持有或由第三方代持有商家股权（但通过公开的证券交易市场且低于发行在外 5%的权 益、通过直接或间接持有无实际控制权的基金、或通过受益人非本人或利益代言人的信托方式持有的股份除外）， 亦不得聘用热爱云居员工（包括但不限于建立正式劳动关系、劳务派遣、外包服务、兼职、咨询顾问等其他形式）。（4）商家不得雇佣热爱云居辞退的人员或自热爱云居主动离职不到 1 年的人员对接热爱云居业务。（5）商家不得通过不正当利益贿赂热爱云居客户，要求客户购买商家商品。</p>
          <p>4、若商家违反上述约定行为之一，热爱云居有权单方部分或全部终止与商家的合同，同时商家应向热爱云居支付 10 万元违约金或者支付合作期间订单（合同）金额的60%作为违约金，两者以高者为准。鉴于上述行为严重破坏经营秩序，损害营商环境，商家充分知悉并认可上述违约金为惩罚性违约金，违约金包括但不限于热爱云居的实际损失、预期利益损失、商誉损失及其他直接和间接损失等。在任何情况下，商家均同意按照本条款约定全额支付违约金。商家应于热爱云居发现违约行为之日起 5 个工作日内支付违约金，如未及时支付，热爱云居有权停止结算货款、质保金、通知第三方支付机构止付商家账户，且有权从合同款项中直接扣除。同时，商家及其实际控制、代理的或协助商家业务的公司将被列入失信名单，即为永不合作的商家。</p>
          <p>5、若商家违反本协议第三条第（2）款和/或第（3）款、第（4）款之规定，除应根据上述第四条承担违约金，商家还应将因此行为所得的全部收益支付给热爱云居。商家应于热爱云居发现该等违约行为之日起 5个工作日内向热爱云居支付其所得的全部收益，如未及时支付，热爱云居有权从合同款项中直接扣除，不足部分热爱云居有权向商家进行追偿。</p>
          <p>6、对于商家，无论是主动还是被动发生第一条、第二条、第三条所示行为的，如果主动向热爱云居提供有效信息，热爱云居将与商家继续合作并给予相应的奖励，对于上述情形的处理热爱云居有完全的判断权和自主权，商家认可并自愿接受处理结果。</p>
          <p>7、若商家有知悉/怀疑热爱云居员工有违反上述规定的，欢迎与热爱云居监察部联系。信息提供者提供的有关商业贿赂行为的信息一经查实，热爱云居将根据事件的影响程度给予信息提供者 5000 元至 5万元人民币的现金奖励或相应广告、促销等资源类奖励（最低不少于人民币 5000 元）。</p>
          <p>热爱云居设定专用邮箱接受商家的投诉 <EMAIL> ，电话：13925549389。热爱云居会对所有信息提供者及所提供的全部资料严格保密</p>
        </div>
      </div>

      <div class="button-group">
        <el-button class="back-btn" @click="goBack">上一步</el-button>
        <el-button type="primary" class="evaluate-btn" :disabled="!agreeToTerms" @click="goToEvaluate">
          去评估
        </el-button>
      </div>
      <div class="agreement-footer">
        <div class="agreement-checkbox">
          <el-checkbox v-model="agreeToTerms" class="checkbox-item">
            我已阅读并同意以上协议
          </el-checkbox>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElButton, ElCheckbox, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()

// 协议同意状态
const agreeToTerms = ref(false)

// 返回上一步
const goBack = () => {
  router.go(-1) // 返回上一页
}

// 去评估页面
const goToEvaluate = () => {
  if (!agreeToTerms.value) {
    ElMessage.warning('请先阅读并同意协议')
    return
  }

  // 这里可以添加实际的跳转逻辑
  console.log('用户已同意协议，跳转到评估页面')
  ElMessage.success('协议已确认，正在跳转...')
  // router.push('/evaluate') // 根据实际路由进行跳转
}
</script>

<style scoped>
.login-container {
  margin: 10px;
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  display: flex;
  flex-direction: row;
}

.login-left-box {
  background-image: url('../assets/banner.png');
  width: 1184px;
  height: 940px;
  background-size: cover;
  background-position: center;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}

.main-logo {
  width: 722px;
  height: 338px;
}

.login-right-box {
  display: flex;
  width: 706px;
  padding: 40px;
  flex-direction: column;
  flex-shrink: 0;
  align-self: stretch;
  border-radius: 20px;
  background: #ffffff;
}

.logo {
  height: 45px;
  width: auto;
  align-self: flex-start;
  margin-bottom: 60px;
}

/* 入驻协议标题样式 */
.agreement-title {
  color: var(--text-1, #1D2129);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 24px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  margin-bottom: 16px;
}

/* 协议内容区域 */
.agreement-content {
  flex: 1;
  overflow-y: auto;
  border-radius: 10px;
  border: 1px solid var(--border-2, #E5E6EB);
  background: var(--fill-2, #F2F3F5);
  display: flex;
  padding: 16px;
  flex-direction: column;
  align-items: flex-start;
  flex: 1 0 0;
  align-self: stretch;
  margin-bottom: 24px;
}

.agreement-content h3 {
  color: var(--text-1, #1D2129);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  margin: 0;
}

.agreement-text {
  color: var(--text-2, #4E5969);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-weight: 400;
  line-height: 1.6;
}

.agreement-text p {
  color: var(--text-2, #4E5969);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.agreement-text h4 {
  color: var(--text-2, #4E5969);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

.agreement-text p:last-child {
  margin-bottom: 0;
}

/* 自动缩进：从"前言"开始的所有h3和p标签都缩进 */
.agreement-text h3:not(:first-child),
.agreement-text p:not(:first-child),
.agreement-text h4 {
  text-indent: 1em;
}

/* 底部操作区域 */
.agreement-footer {
  margin-top: auto;
  padding-top: 0px;
}

.agreement-checkbox {
  margin-bottom: 10px;
}

:deep(.checkbox-item .el-checkbox__label) {
  color: var(--text-3, #86909C);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

:deep(.checkbox-item.is-checked .el-checkbox__label) {
  color: var(--text-3, #86909C) !important;
}

.button-group {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.back-btn {
  flex: 1;
  height: 48px;
  border-radius: 10px;
  border: 1px solid #E5E6EB;
  color: var(--text-2, #4E5969);
  font-family: "Alibaba PuHuiTi 2.0";
  border-radius: 10px;
  background: var(--fill-3, #E5E6EB);
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 1.8px;
}

.back-btn:hover {
  background: var(--fill-3, #E5E6EB);
  border-color: #E5E6EB;
  color: var(--text-2, #4E5969);;
}

.evaluate-btn {
  flex: 1;
  height: 48px;
  border-radius: 10px;
  font-size: 18px;
  font-family: "Alibaba PuHuiTi 2.0";
  border: 1px solid rgba(255, 255, 255, 0.20);
  background: linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%), linear-gradient(0deg, #2F7DFB 0%, #2F7DFB 100%), linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%);
  color: #FFF;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 1.8px;
}

.evaluate-btn.is-disabled:hover {
  background: #F2F3F5;
  border-color: #E5E6EB;
  color: #C9CDD4;
}

:deep(.evaluate-btn.is-disabled) {
  background: #F2F3F5;
  border-color: #E5E6EB;
  color: #C9CDD4;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
    margin: 0;
    width: 100%;
    height: 100vh;
  }

  .login-left-box {
    width: 100%;
    height: 40vh;
    min-height: 300px;
    border-radius: 0 0 20px 20px;
  }

  .main-logo {
    width: 60vw;
    height: auto;
    max-width: 400px;
  }

  .login-right-box {
    width: 100%;
    height: 60vh;
    padding: 30px 20px;
    border-radius: 20px 20px 0 0;
    margin-top: -20px;
    z-index: 10;
    position: relative;
    display: flex;
    flex-direction: column;
  }

  .logo {
    height: 35px;
    margin-bottom: 30px;
  }

  .agreement-title {
    font-size: 20px;
    margin-bottom: 15px;
  }

  .agreement-content {
    flex: 1;
    padding: 15px;
    margin-bottom: 15px;
  }

  .agreement-text {
    font-size: 13px;
    line-height: 1.5;
  }

  .agreement-text p {
    margin-bottom: 10px;
  }

  .button-group {
    gap: 8px;
  }

  .back-btn,
  .evaluate-btn {
    height: 44px;
    font-size: 15px;
  }

  :deep(.checkbox-item .el-checkbox__label) {
    font-size: 13px;
  }
}

/* 小屏幕手机适配 */
@media (max-width: 480px) {
  .login-right-box {
    padding: 20px 16px;
  }

  .agreement-title {
    font-size: 18px;
    margin-bottom: 12px;
  }

  .agreement-content {
    padding: 12px;
    margin-bottom: 12px;
  }

  .agreement-text {
    font-size: 12px;
    line-height: 1.4;
  }

  .agreement-text p {
    margin-bottom: 8px;
  }

  .button-group {
    gap: 6px;
  }

  .back-btn,
  .evaluate-btn {
    height: 42px;
    font-size: 14px;
  }

  :deep(.checkbox-item .el-checkbox__label) {
    font-size: 12px;
  }
}

@font-face {
  font-family: "江城斜黑体";
  src: url("../assets/typeface/JiangXieHei500W.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "江城斜黑体";
  src: url("../assets/typeface/JiangXieHei900W.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

</style>